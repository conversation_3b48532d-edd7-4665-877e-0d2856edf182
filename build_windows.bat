@echo off
REM Build script for Windows

echo Building Monty for Windows...
echo.

REM Check if cargo is available
where cargo >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Rust/Cargo not found. Please install Rust from https://rustup.rs/
    exit /b 1
)

REM Set optimization flags
set RUSTFLAGS=-Ctarget-cpu=native

REM Build with embedded networks (default)
echo Building release version with embedded networks...
cargo build --release --bin monty --features=embed

if %errorlevel% equ 0 (
    echo.
    echo Build successful! Executable is at: target\release\monty.exe
    echo.
    echo To build other variants:
    echo   - Without embedded networks: cargo build --release --bin monty
    echo   - For testing: cargo build --release --bin monty --features=uci-minimal,tunable
    echo   - Data generator: cargo build --release --package datagen --bin datagen
) else (
    echo.
    echo Build failed!
    exit /b 1
)