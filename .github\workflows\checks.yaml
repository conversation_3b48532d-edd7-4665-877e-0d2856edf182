on:
  pull_request:
  push:
    branches:
      - master


name: Basic Checks
jobs:
  check:
    name: cargo check
    runs-on: ubuntu-latest
    steps:
        - uses: actions/checkout@v4
        - uses: dtolnay/rust-toolchain@stable
        - run: cargo check
        - run: cargo check --package datagen

  clippy:
    name: cargo clippy
    runs-on: ubuntu-latest
    steps:
        - uses: actions/checkout@v4
        - uses: dtolnay/rust-toolchain@stable
        - run: cargo clippy -- -D warnings
        - run: cargo clippy --package datagen -- -D warnings

  fmt:
    name: cargo fmt
    runs-on: ubuntu-latest
    steps:
        - uses: actions/checkout@v4
        - uses: dtolnay/rust-toolchain@stable
        - run: cargo fmt --all -- --check
