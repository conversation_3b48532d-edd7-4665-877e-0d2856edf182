# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Monty is a chess engine built in Rust that uses Monte Carlo Tree Search (MCTS) with neural networks for position evaluation. It implements the UCI protocol and supports Fischer Random Chess.

## Development Commands

### Build Commands
- `make` - Standard build with embedded networks
- `make raw` - Build with raw (uncompressed) networks  
- `make montytest` - Build for testing with minimal UCI and tunable parameters
- `make noembed` - Build without embedded networks
- `make gen` - Build data generation tool

### Runtime
- `./monty` - Run UCI engine
- `./monty bench` - Run benchmark

### Testing
Uses [montytest](https://tests.montychess.org/tests) framework. Functional patches must pass STC (Short Time Control) followed by LTC (Long Time Control) tests.

## Architecture

### Core Modules
- **`mcts/`** - Monte Carlo Tree Search implementation (main development focus)
  - `iteration.rs` - Search iterations
  - `params.rs` - Search parameters
  - `helpers.rs` - Search utilities
- **`chess/`** - Chess logic (board, moves, attacks, FRC support)
- **`networks/`** - Neural network evaluation (policy and value networks)
- **`tree/`** - Search tree management with threading support
- **`datagen/`** - Self-play data generation for training

### Key Features
- Neural networks are automatically downloaded and validated by SHA-256 hash
- Networks can be embedded (compressed with zstd) or external
- Multi-threaded search with sophisticated tree management
- Memory-mapped neural networks for performance

### Feature Flags
- `embed` - Embed compressed networks in binary
- `raw` - Use uncompressed networks
- `datagen` - Enable data generation
- `uci-minimal` - Minimal UCI implementation
- `tunable` - Enable parameter tuning

## Build Requirements
- Rust 1.83+
- Git (for build metadata)
- curl (for network downloads)
- Use `RUSTFLAGS="-Ctarget-cpu=native"` for performance optimization

## Async Integration
- **Full Tokio Runtime**: Uses `#[tokio::main]` for async execution
- **Async UCI Communication**: Non-blocking stdin/stdout with proper stream handling
- **Async Search Coordination**: Search tasks with cancellation support via tokio::select!
- **Async Network Operations**: Network downloads with timeouts and error handling
- **Async Data Generation**: Concurrent data generation with async file I/O
- **Responsive Engine**: Input handling is non-blocking for better UCI responsiveness

### Key Async Features:
- Non-blocking UCI command processing
- Concurrent search execution with input monitoring
- Async file operations for data generation
- Timeout-based network downloads
- Graceful task cancellation and cleanup

## Environment
- You are running in WSL (ubuntu environment)
- MSYS2 installed at C:\msys64 with MinGW64 toolchain available
- PowerShell build script supports both MSVC and GNU toolchains

## Build Status
- ✅ All compilation errors resolved
- ✅ Async implementation working correctly  
- ✅ Ready to build with `.\build.ps1 -UseGnuTarget`

## Path and Tools
Add this to your memory. Path and tools etc.