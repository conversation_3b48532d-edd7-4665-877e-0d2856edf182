# Build script for Windows (PowerShell)

Write-Host "Building Monty for Windows..." -ForegroundColor Green
Write-Host ""

# Check if cargo is available
if (-not (Get-Command cargo -ErrorAction SilentlyContinue)) {
    Write-Host "Error: Rust/Cargo not found. Please install Rust from https://rustup.rs/" -ForegroundColor Red
    exit 1
}

# Set optimization flags
$env:RUSTFLAGS = "-Ctarget-cpu=native"

# Build with embedded networks (default)
Write-Host "Building release version with embedded networks..." -ForegroundColor Yellow
cargo clean
cargo build --release --bin monty --features="embed"

if ($LASTEXITCODE -eq 0) {
    Write-Host ""
    Write-Host "Build successful!" -ForegroundColor Green
    Write-Host "Executable is at: target\release\monty.exe" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "To build other variants:" -ForegroundColor Yellow
    Write-Host "  - Without embedded networks: cargo build --release --bin monty"
    Write-Host "  - For testing: cargo build --release --bin monty --features=uci-minimal,tunable"
    Write-Host "  - Data generator: cargo build --release --package datagen --bin datagen"
} else {
    Write-Host ""
    Write-Host "Build failed!" -ForegroundColor Red
    exit 1
}