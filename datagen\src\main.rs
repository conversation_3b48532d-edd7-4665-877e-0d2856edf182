mod rng;
mod thread;

use montyformat::{Monty<PERSON><PERSON><PERSON>, MontyValueFormat};
use rng::Rand;
use thread::DatagenThread;

use monty::{
    chess::ChessState,
    mcts::MctsParams,
    networks::{self, PolicyNetwork, ValueNetwork},
    read_into_struct_unchecked, uci, MappedWeights,
};

use std::{
    env::Args,
    fs::File,
    io::{BufWriter, Write},
    sync::{
        atomic::{AtomicBool, Ordering},
        Arc, Mutex,
    },
    time::Duration,
};

fn main() {
    let mut args = std::env::args();
    args.next();

    let policy_mapped: MappedWeights<networks::PolicyNetwork> =
        unsafe { read_into_struct_unchecked(networks::PolicyFileDefaultName) };

    let value_mapped: MappedWeights<networks::ValueNetwork> =
        unsafe { read_into_struct_unchecked(networks::ValueFileDefaultName) };

    let policy = &policy_mapped.data;
    let value = &value_mapped.data;

    let params = MctsParams::default();

    if let Some(opts) = parse_args(args) {
        run_datagen(params, opts, policy, value);
    } else {
        uci::bench(ChessState::BENCH_DEPTH, policy, value, &params);
    }
}

pub fn to_slice_with_lifetime<T, U>(slice: &[T]) -> &[U] {
    let src_size = std::mem::size_of_val(slice);
    let tgt_size = std::mem::size_of::<U>();

    assert!(
        src_size % tgt_size == 0,
        "Target type size does not divide slice size!"
    );

    let len = src_size / tgt_size;
    unsafe { std::slice::from_raw_parts(slice.as_ptr().cast(), len) }
}

pub struct Destination {
    writer: BufWriter<File>,
    reusable_buffer: Vec<u8>,
    games: usize,
    limit: usize,
    results: [usize; 3],
}

impl Destination {
    pub fn push(&mut self, game: &MontyValueFormat, stop: &AtomicBool) {
        if stop.load(Ordering::Relaxed) {
            return;
        }

        let result = (2.0 * game.result) as usize;
        self.results[result] += 1;
        self.games += 1;
        game.serialise_into(&mut self.writer).unwrap();

        if self.games >= self.limit {
            stop.store(true, Ordering::Relaxed);
            return;
        }

        if self.games % 32 == 0 {
            self.report();
        }
    }

    pub fn push_policy(&mut self, game: &MontyFormat, stop: &AtomicBool) {
        if stop.load(Ordering::Relaxed) {
            return;
        }

        let result = (game.result * 2.0) as usize;
        self.results[result] += 1;
        self.games += 1;

        game.serialise_into_buffer(&mut self.reusable_buffer)
            .unwrap();
        self.writer.write_all(&self.reusable_buffer).unwrap();
        self.reusable_buffer.clear();

        if self.games >= self.limit {
            stop.store(true, Ordering::Relaxed);
            return;
        }

        if self.games % 32 == 0 {
            self.report();
        }
    }

    pub fn report(&self) {
        println!(
            "finished games {} losses {} draws {} wins {}",
            self.games, self.results[0], self.results[1], self.results[2],
        )
    }
}

#[allow(clippy::too_many_arguments)]
pub fn run_datagen(
    params: MctsParams,
    opts: RunOptions,
    policy: &PolicyNetwork,
    value: &ValueNetwork,
) {
    println!("{opts:#?}");

    let stop_base = Arc::new(AtomicBool::new(false));

    let dest = Destination {
        writer: std::io::BufWriter::new(std::fs::File::create(&opts.out_path).unwrap()),
        reusable_buffer: Vec::new(),
        games: 0,
        limit: opts.games,
        results: [0; 3],
    };

    let dest_mutex = Arc::new(Mutex::new(dest));

    let book: Option<Vec<String>> = if let Some(path) = &opts.book {
        let content = std::fs::read_to_string(path)
            .expect("Failed to read book file");
        Some(content.lines().map(|s| s.to_string()).collect())
    } else {
        None
    };

    std::thread::scope(|s| {
        for i in 0..opts.threads {
            let params = params.clone();
            std::thread::sleep(Duration::from_millis(i as u64 * 10));
            let this_book = book.clone();
            let this_dest = dest_mutex.clone();
            let stop_ref = stop_base.clone();
            
            s.spawn(move || {
                let mut thread = DatagenThread::new(params.clone(), &stop_ref, this_book, this_dest);
                thread.run(opts.nodes, opts.policy_data, policy, value);
            });
        }
    });

    let dest = dest_mutex.lock().unwrap();
    dest.report();
}


#[derive(Debug, Default)]
pub struct RunOptions {
    games: usize,
    threads: usize,
    book: Option<String>,
    policy_data: bool,
    nodes: usize,
    out_path: String,
}

pub fn parse_args(args: Args) -> Option<RunOptions> {
    let mut opts = RunOptions::default();

    let mut mode = 0;

    for arg in args {
        match arg.as_str() {
            "bench" => return None,
            "--policy-data" => opts.policy_data = true,
            "-t" | "--threads" => mode = 1,
            "-b" | "--book" => mode = 2,
            "-n" | "--nodes" => mode = 3,
            "-o" | "--output" => mode = 4,
            "-g" | "--games" => mode = 5,
            _ => match mode {
                1 => {
                    opts.threads = arg.parse().expect("can't parse");
                    mode = 0;
                }
                2 => {
                    opts.book = Some(arg);
                    mode = 0;
                }
                3 => {
                    opts.nodes = arg.parse().expect("can't parse");
                    mode = 0;
                }
                4 => {
                    opts.out_path = arg;
                    mode = 0;
                }
                5 => {
                    opts.games = arg.parse().expect("can't parse");
                    mode = 0;
                }
                _ => println!("unrecognised argument {arg}"),
            },
        }
    }

    Some(opts)
}
