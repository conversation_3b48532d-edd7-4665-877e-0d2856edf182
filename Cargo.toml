[package]
name = "monty"
version = "1.0.0"
edition = "2021"
authors = ["<PERSON>"]
rust-version = "1.83"

[profile.release]
panic = 'abort'
strip = true
lto = true
codegen-units = 1

[features]
embed = []
raw = []
datagen = []
uci-minimal = []
tunable = []
thompson = []

[workspace]
members = ["datagen"]
resolver = "2"

[dependencies]
memmap2 = "0.9.5"
zstd = "0.13.2"
once_cell = "1.20.2"
sha2 = "0.10.8"
tokio = { version = "1.0", features = ["full"] }
fastrand = "2.0"

[build-dependencies]
sha2 = "0.10.8"
chrono = "0.4.38"
zstd = { version = "0.13.2", features = ["zstdmt"] }
tokio = { version = "1.0", features = ["rt", "process", "fs"] }